#!/usr/bin/env python3
"""
改进的视频流处理模块
解决RTMP流超时和连接问题
"""

import cv2
import time
import threading
import requests
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtGui import QImage

class ImprovedVideoThread(QThread):
    """改进的视频线程，解决RTMP超时问题"""
    change_pixmap = pyqtSignal(QImage)
    update_counts = pyqtSignal(dict)
    connection_status = pyqtSignal(str)  # 连接状态信号

    def __init__(self, rtmp_url, api_url):
        super().__init__()
        self.rtmp_url = rtmp_url
        self.api_url = api_url
        self.running = False
        self.cap = None
        self.retry_count = 0
        self.max_retries = 10
        self.connection_timeout = 15  # 15秒连接超时
        
    def setup_capture(self):
        """设置VideoCapture参数"""
        if self.cap:
            self.cap.release()
            
        # 创建VideoCapture对象
        self.cap = cv2.VideoCapture(self.rtmp_url, cv2.CAP_FFMPEG)
        
        # 设置关键参数
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区
        self.cap.set(cv2.CAP_PROP_FPS, 30)  # 目标帧率
        
        # 设置超时参数（如果支持）
        try:
            self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 10000)  # 10秒打开超时
            self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)   # 5秒读取超时
        except:
            pass  # 某些OpenCV版本可能不支持这些参数
            
        return self.cap.isOpened()

    def wait_for_stream(self):
        """等待RTMP流可用"""
        print(f"🔄 等待RTMP流启动: {self.rtmp_url}")
        
        for attempt in range(30):  # 最多等待30秒
            if not self.running:
                return False
                
            try:
                # 检查服务器状态
                response = requests.get(f"{self.api_url}/status", timeout=2)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 'running':
                        print("✅ 服务器确认流已启动")
                        break
            except:
                pass
                
            print(f"⏳ 等待流启动... ({attempt + 1}/30)")
            time.sleep(1)
        
        # 尝试连接RTMP流
        for attempt in range(10):
            if not self.running:
                return False
                
            print(f"🔗 尝试连接RTMP流... ({attempt + 1}/10)")
            
            if self.setup_capture():
                # 测试读取一帧
                ret, frame = self.cap.read()
                if ret and frame is not None:
                    print("✅ RTMP流连接成功")
                    return True
                    
            time.sleep(2)
            
        return False

    def run(self):
        """主运行循环"""
        self.running = True
        self.connection_status.emit("🔄 正在连接...")
        
        # 等待流可用
        if not self.wait_for_stream():
            self.connection_status.emit("❌ 无法连接到RTMP流")
            print("❌ 无法连接到RTMP流")
            return
            
        self.connection_status.emit("✅ 已连接")
        
        frame_count = 0
        last_count_update = 0
        consecutive_failures = 0
        max_consecutive_failures = 30  # 连续失败30次后重连
        
        print("🎬 开始视频流处理...")

        while self.running:
            try:
                ret, frame = self.cap.read()
                
                if ret and frame is not None:
                    consecutive_failures = 0  # 重置失败计数
                    
                    # 转换图像格式
                    rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    h, w, ch = rgb_image.shape
                    bytes_per_line = ch * w
                    qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
                    self.change_pixmap.emit(qt_image)

                    # 定期获取计数
                    frame_count += 1
                    if frame_count - last_count_update >= 30:  # 每30帧
                        self.get_counts()
                        last_count_update = frame_count
                        
                    # 控制帧率
                    self.msleep(33)  # 约30fps
                    
                else:
                    consecutive_failures += 1
                    print(f"⚠️ 读取帧失败 ({consecutive_failures}/{max_consecutive_failures})")
                    
                    if consecutive_failures >= max_consecutive_failures:
                        print("🔄 连续失败过多，尝试重新连接...")
                        self.connection_status.emit("🔄 重新连接中...")
                        
                        if self.wait_for_stream():
                            consecutive_failures = 0
                            self.connection_status.emit("✅ 重新连接成功")
                        else:
                            print("❌ 重新连接失败")
                            self.connection_status.emit("❌ 连接丢失")
                            break
                    else:
                        self.msleep(100)  # 短暂等待
                        
            except Exception as e:
                print(f"❌ 视频处理错误: {e}")
                consecutive_failures += 1
                self.msleep(100)

        # 清理资源
        if self.cap:
            self.cap.release()
        print("🛑 视频线程已停止")
        self.connection_status.emit("⏹️ 已断开")

    def get_counts(self):
        """获取检测计数"""
        try:
            response = requests.get(f"{self.api_url}/status", timeout=2)
            if response.status_code == 200:
                data = response.json()
                counts = data.get('counts', {})
                if counts:
                    self.update_counts.emit(counts)
        except Exception as e:
            # 静默处理计数获取错误，不影响视频流
            pass

    def stop(self):
        """停止线程"""
        print("🛑 正在停止视频线程...")
        self.running = False
        self.wait(5000)  # 等待最多5秒
        
        if self.cap:
            self.cap.release()
            self.cap = None

#!/usr/bin/env python3
"""
完整系统设置和测试脚本
包括FFmpeg检查、RTMP流测试、系统验证
"""

import os
import sys
import time
import subprocess
import requests
import threading
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔧 {title}")
    print("=" * 60)

def check_ffmpeg():
    """检查FFmpeg安装"""
    print_header("检查FFmpeg安装")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg已安装: {version_line}")
            return True
        else:
            print("❌ FFmpeg命令执行失败")
            return False
            
    except FileNotFoundError:
        print("❌ FFmpeg未找到")
        print("📥 请运行以下命令安装FFmpeg:")
        print("   1. 运行 install_ffmpeg.bat (需要管理员权限)")
        print("   2. 或手动下载: https://www.gyan.dev/ffmpeg/builds/")
        print("   3. 解压到 C:\\ffmpeg\\ 并添加到PATH")
        return False
        
    except Exception as e:
        print(f"❌ 检查FFmpeg时出错: {e}")
        return False

def check_python_packages():
    """检查Python包"""
    print_header("检查Python依赖包")
    
    required_packages = [
        'PyQt5', 'opencv-python', 'ultralytics', 
        'flask', 'flask-restx', 'requests', 'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📦 缺少包: {', '.join(missing_packages)}")
        print("运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有Python包已安装")
    return True

def check_model_file():
    """检查模型文件"""
    print_header("检查YOLOv8模型文件")
    
    model_path = Path("server/models/best.pt")
    if model_path.exists():
        size_mb = model_path.stat().st_size / (1024 * 1024)
        print(f"✅ 模型文件存在: {model_path} ({size_mb:.1f} MB)")
        return True
    else:
        print(f"❌ 模型文件不存在: {model_path}")
        print("请确保YOLOv8模型文件位于正确位置")
        return False

def test_server_startup():
    """测试服务器启动"""
    print_header("测试服务器启动")
    
    try:
        # 检查是否已有服务器运行
        try:
            response = requests.get("http://localhost:5000/api/status", timeout=2)
            if response.status_code == 200:
                print("✅ 服务器已在运行")
                return True
        except:
            pass
        
        print("🚀 启动服务器...")
        
        # 启动服务器进程
        if check_ffmpeg():
            cmd = [sys.executable, "simple_start.py"]
        else:
            cmd = [sys.executable, "simple_start_no_ffmpeg.py"]
            
        process = subprocess.Popen(cmd, 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待服务器启动
        for i in range(30):  # 最多等待30秒
            try:
                response = requests.get("http://localhost:5000/api/status", timeout=1)
                if response.status_code == 200:
                    print("✅ 服务器启动成功")
                    return True
            except:
                pass
            
            print(f"⏳ 等待服务器启动... ({i+1}/30)")
            time.sleep(1)
        
        print("❌ 服务器启动超时")
        process.terminate()
        return False
        
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print_header("测试API端点")
    
    base_url = "http://localhost:5000/api"
    
    # 测试状态端点
    try:
        response = requests.get(f"{base_url}/status", timeout=5)
        if response.status_code == 200:
            print("✅ /api/status 端点正常")
            data = response.json()
            print(f"   状态: {data.get('status', 'unknown')}")
        else:
            print(f"❌ /api/status 返回错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ /api/status 测试失败: {e}")
        return False
    
    print("✅ API端点测试通过")
    return True

def test_client_startup():
    """测试客户端启动"""
    print_header("测试客户端启动")
    
    try:
        print("🖥️ 启动客户端GUI...")
        print("注意: 客户端将在新窗口中打开")
        print("请手动检查GUI是否正常显示")
        
        # 启动客户端（非阻塞）
        process = subprocess.Popen([sys.executable, "client/main.py"],
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        # 等待几秒检查是否正常启动
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ 客户端启动成功")
            print("💡 请检查GUI窗口是否正常显示")
            
            # 询问用户是否要关闭客户端
            input("按回车键继续测试（客户端将继续运行）...")
            return True
        else:
            stdout, stderr = process.communicate()
            print("❌ 客户端启动失败")
            if stderr:
                print(f"错误信息: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        return False

def generate_report(results):
    """生成测试报告"""
    print_header("系统测试报告")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
    print()
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print()
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！系统已准备就绪")
        print()
        print("🚀 启动说明:")
        print("   1. 服务器: python simple_start.py")
        print("   2. 客户端: python client/main.py")
    else:
        print("⚠️ 部分测试失败，请检查上述问题")
        print()
        print("🔧 常见解决方案:")
        if not results.get("FFmpeg检查", True):
            print("   - 安装FFmpeg: 运行 install_ffmpeg.bat")
        if not results.get("Python包检查", True):
            print("   - 安装依赖: pip install -r requirements.txt")
        if not results.get("模型文件检查", True):
            print("   - 确保YOLOv8模型文件存在")

def main():
    """主函数"""
    print("🔧 YOLOv8计数系统 - 完整设置和测试")
    print("=" * 60)
    
    # 切换到项目目录
    os.chdir(Path(__file__).parent)
    
    # 执行测试
    results = {}
    
    results["FFmpeg检查"] = check_ffmpeg()
    results["Python包检查"] = check_python_packages()
    results["模型文件检查"] = check_model_file()
    
    if all(results.values()):
        results["服务器启动"] = test_server_startup()
        if results["服务器启动"]:
            results["API测试"] = test_api_endpoints()
            results["客户端启动"] = test_client_startup()
    
    # 生成报告
    generate_report(results)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")

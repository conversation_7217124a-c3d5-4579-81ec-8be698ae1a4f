import sys
from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QComboBox, QFrame,
                             QGroupBox, QGridLayout)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QImage, QPixmap, QFont
import cv2
import requests

# 导入改进的视频流模块
try:
    from video_stream_improved import ImprovedVideoThread
    USE_IMPROVED_STREAM = True
except ImportError:
    USE_IMPROVED_STREAM = False
    print("⚠️ 使用标准视频流模块")


class VideoThread(QThread):
    change_pixmap = pyqtSignal(QImage)
    update_counts = pyqtSignal(dict)

    def __init__(self, rtmp_url, api_url):
        super().__init__()
        self.rtmp_url = rtmp_url
        self.api_url = api_url
        self.running = False
        self.cap = None
        self.retry_count = 0
        self.max_retries = 5

    def run(self):
        self.running = True

        # 配置OpenCV VideoCapture参数以减少超时
        self.cap = cv2.VideoCapture(self.rtmp_url)

        # 设置缓冲区大小和超时参数
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲区
        self.cap.set(cv2.CAP_PROP_FPS, 30)  # 设置帧率

        # 尝试连接
        connection_timeout = 10  # 10秒连接超时
        frame_count = 0
        last_count_update = 0

        print(f"🔄 尝试连接RTMP流: {self.rtmp_url}")

        while self.running:
            ret, frame = self.cap.read()
            if ret:
                self.retry_count = 0  # 重置重试计数

                # 转换图像格式
                rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                h, w, ch = rgb_image.shape
                bytes_per_line = ch * w
                qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
                self.change_pixmap.emit(qt_image)

                # 每30帧获取一次计数（约1秒）
                frame_count += 1
                if frame_count - last_count_update >= 30:
                    self.get_counts()
                    last_count_update = frame_count

                # 短暂延迟以控制帧率
                self.msleep(33)  # 约30fps

            else:
                print(f"⚠️ 读取帧失败，重试次数: {self.retry_count}")
                self.retry_count += 1

                if self.retry_count >= self.max_retries:
                    print("❌ 达到最大重试次数，停止视频流")
                    break

                # 重新初始化VideoCapture
                if self.cap:
                    self.cap.release()

                print(f"🔄 重新连接RTMP流...")
                self.msleep(1000)  # 等待1秒后重试

                self.cap = cv2.VideoCapture(self.rtmp_url)
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                self.cap.set(cv2.CAP_PROP_FPS, 30)

        if self.cap:
            self.cap.release()
        print("🛑 视频线程已停止")

    def get_counts(self):
        try:
            response = requests.get(f"{self.api_url}/status")
            if response.status_code == 200:
                data = response.json()
                self.update_counts.emit(data.get('counts', {}))
        except Exception as e:
            print(f"获取计数错误: {e}")

    def stop(self):
        self.running = False
        self.wait()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("电子元器件计数系统")
        self.setGeometry(100, 100, 800, 600)

        # 配置
        self.rtmp_url = "rtmp://localhost/live/stream"
        self.api_url = "http://localhost:5000/api"

        # 累计计数器
        self.cumulative_counts = {}
        self.cumulative_labels = {}
        self.session_start_time = None

        # 初始化UI
        self.init_ui()

        # 视频线程
        self.video_thread = None

        # 状态检查定时器
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.check_server_status)
        self.status_timer.start(1000)  # 每秒检查一次

    def init_ui(self):
        # 主部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # 视频显示区域
        self.video_label = QLabel()
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setMinimumSize(640, 640)
        self.video_label.setStyleSheet("background-color: black;")
        main_layout.addWidget(self.video_label)

        # 控制面板
        control_group = QGroupBox("🎛️ 检测参数设置")
        control_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        control_layout = QGridLayout()
        control_group.setLayout(control_layout)

        # 摄像头选择
        camera_label = QLabel("📷 摄像头:")
        camera_label.setStyleSheet("font-size: 14px; color: #34495e;")
        self.camera_combo = QComboBox()
        self.camera_combo.addItems(["摄像头0", "摄像头1", "摄像头2"])
        self.camera_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        control_layout.addWidget(camera_label, 0, 0)
        control_layout.addWidget(self.camera_combo, 0, 1)

        # 分辨率选择
        resolution_label = QLabel("📐 分辨率:")
        resolution_label.setStyleSheet("font-size: 14px; color: #34495e;")
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["480x480", "640x640", "800x600"])
        self.resolution_combo.setCurrentText("640x640")
        self.resolution_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        control_layout.addWidget(resolution_label, 0, 2)
        control_layout.addWidget(self.resolution_combo, 0, 3)

        # 帧率选择
        fps_label = QLabel("🎬 帧率:")
        fps_label.setStyleSheet("font-size: 14px; color: #34495e;")
        self.fps_combo = QComboBox()
        self.fps_combo.addItems(["15", "24", "30", "60"])
        self.fps_combo.setCurrentText("30")
        self.fps_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        control_layout.addWidget(fps_label, 0, 4)
        control_layout.addWidget(self.fps_combo, 0, 5)

        main_layout.addWidget(control_group)

        # 按钮区域
        button_group = QGroupBox("🎮 操作控制")
        button_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        button_layout = QHBoxLayout()
        button_group.setLayout(button_layout)

        # 开始按钮
        self.start_btn = QPushButton("🚀 开始检测")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #2ecc71);
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2ecc71, stop:1 #27ae60);
            }
            QPushButton:pressed {
                background: #229954;
            }
            QPushButton:disabled {
                background: #95a5a6;
            }
        """)
        self.start_btn.clicked.connect(self.start_detection)
        button_layout.addWidget(self.start_btn)

        # 停止按钮
        self.stop_btn = QPushButton("⏹️ 停止检测")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #e74c3c);
            }
            QPushButton:pressed {
                background: #a93226;
            }
            QPushButton:disabled {
                background: #95a5a6;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_detection)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)

        # 重置累计计数按钮
        self.reset_btn = QPushButton("🔄 重置累计")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #f39c12);
            }
            QPushButton:pressed {
                background: #d68910;
            }
        """)
        self.reset_btn.clicked.connect(self.reset_cumulative_counts)
        button_layout.addWidget(self.reset_btn)

        main_layout.addWidget(button_group)

        # 计数显示区域
        count_group = QGroupBox("📊 实时检测统计")
        count_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #9b59b6;
                border-radius: 10px;
                margin: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        count_main_layout = QVBoxLayout()
        count_group.setLayout(count_main_layout)

        # 当前检测计数
        current_count_layout = QHBoxLayout()
        current_label = QLabel("🔍 当前检测:")
        current_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #8e44ad;")
        current_count_layout.addWidget(current_label)

        self.count_layout = QHBoxLayout()
        self.count_labels = {}

        # 初始化当前计数标签
        for component in ["电阻", "电容", "LED"]:  # 根据实际模型类别修改
            label = QLabel(f"{component}: 0")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: bold;
                    color: #2c3e50;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #ecf0f1, stop:1 #bdc3c7);
                    border: 2px solid #95a5a6;
                    border-radius: 8px;
                    padding: 8px;
                    margin: 2px;
                    min-width: 100px;
                }
            """)
            self.count_layout.addWidget(label)
            self.count_labels[component] = label

        current_count_layout.addLayout(self.count_layout)
        count_main_layout.addLayout(current_count_layout)

        # 累计计数
        cumulative_count_layout = QHBoxLayout()
        cumulative_label = QLabel("📈 累计统计:")
        cumulative_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #e74c3c;")
        cumulative_count_layout.addWidget(cumulative_label)

        self.cumulative_layout = QHBoxLayout()
        self.cumulative_labels = {}

        # 初始化累计计数标签
        for component in ["电阻", "电容", "LED"]:
            label = QLabel(f"{component}: 0")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: bold;
                    color: white;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #e74c3c, stop:1 #c0392b);
                    border: 2px solid #a93226;
                    border-radius: 8px;
                    padding: 8px;
                    margin: 2px;
                    min-width: 100px;
                }
            """)
            self.cumulative_layout.addWidget(label)
            self.cumulative_labels[component] = label
            self.cumulative_counts[component] = 0

        cumulative_count_layout.addLayout(self.cumulative_layout)
        count_main_layout.addLayout(cumulative_count_layout)

        main_layout.addWidget(count_group)

        # 状态栏
        self.status_bar = self.statusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #34495e, stop:1 #2c3e50);
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-top: 2px solid #3498db;
            }
        """)
        self.status_bar.showMessage("🟢 系统就绪 - 等待开始检测...")

    def reset_cumulative_counts(self):
        """重置累计计数"""
        for component in self.cumulative_counts:
            self.cumulative_counts[component] = 0
            if component in self.cumulative_labels:
                self.cumulative_labels[component].setText(f"{component}: 0")

        self.session_start_time = None
        self.status_bar.showMessage("🔄 累计计数已重置")

    def start_detection(self):
        # 获取参数
        camera_index = self.camera_combo.currentIndex()
        resolution = self.resolution_combo.currentText()
        fps = int(self.fps_combo.currentText())

        # 设置会话开始时间
        if self.session_start_time is None:
            from datetime import datetime
            self.session_start_time = datetime.now()

        # 发送API请求
        try:
            self.status_bar.showMessage("🔄 正在启动检测系统...")
            response = requests.post(
                f"{self.api_url}/start",
                json={
                    "camera_index": camera_index,
                    "resolution": resolution,
                    "fps": fps
                }
            )

            if response.status_code == 200:
                data = response.json()
                self.status_bar.showMessage(f"✅ 检测已启动 - RTMP: {data.get('rtmp_url', '')}")

                # 启动视频线程
                if self.video_thread is None:
                    if USE_IMPROVED_STREAM:
                        self.video_thread = ImprovedVideoThread(self.rtmp_url, self.api_url)
                        self.video_thread.connection_status.connect(self.update_connection_status)
                    else:
                        self.video_thread = VideoThread(self.rtmp_url, self.api_url)

                    self.video_thread.change_pixmap.connect(self.set_image)
                    self.video_thread.update_counts.connect(self.update_count_labels)
                    self.video_thread.start()

                # 更新按钮状态
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)

                # 清空视频显示的等待文本
                self.video_label.setText("")
            else:
                self.status_bar.showMessage(f"❌ 启动失败: {response.text}")

        except Exception as e:
            self.status_bar.showMessage(f"❌ 连接服务器失败: {str(e)}")

    def stop_detection(self):
        try:
            self.status_bar.showMessage("🔄 正在停止检测...")
            response = requests.get(f"{self.api_url}/stop")

            if response.status_code == 200:
                self.status_bar.showMessage("⏹️ 检测已停止")

                # 停止视频线程
                if self.video_thread is not None:
                    self.video_thread.stop()
                    self.video_thread = None

                # 更新按钮状态
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)

                # 恢复视频显示等待状态
                self.video_label.clear()
                self.video_label.setText("📹 等待视频流...")
                self.video_label.setStyleSheet("""
                    QLabel {
                        background-color: #34495e;
                        border-radius: 10px;
                        color: #ecf0f1;
                        font-size: 16px;
                    }
                """)

            else:
                self.status_bar.showMessage(f"❌ 停止失败: {response.text}")

        except Exception as e:
            self.status_bar.showMessage(f"❌ 连接服务器失败: {str(e)}")

    def set_image(self, image):
        self.video_label.setPixmap(QPixmap.fromImage(image).scaled(
            self.video_label.width(),
            self.video_label.height(),
            Qt.KeepAspectRatio
        ))

    def update_count_labels(self, counts):
        for component, count in counts.items():
            # 更新当前检测计数
            if component in self.count_labels:
                self.count_labels[component].setText(f"{component}: {count}")
            else:
                # 动态添加新组件计数
                label = QLabel(f"{component}: {count}")
                label.setAlignment(Qt.AlignCenter)
                label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: #2c3e50;
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #ecf0f1, stop:1 #bdc3c7);
                        border: 2px solid #95a5a6;
                        border-radius: 8px;
                        padding: 8px;
                        margin: 2px;
                        min-width: 100px;
                    }
                """)
                self.count_layout.addWidget(label)
                self.count_labels[component] = label

            # 更新累计计数
            if component not in self.cumulative_counts:
                self.cumulative_counts[component] = 0

            # 如果当前检测到的数量大于之前的最大值，更新累计计数
            if count > 0:
                if count > self.cumulative_counts[component]:
                    self.cumulative_counts[component] = count

            # 更新累计显示
            if component in self.cumulative_labels:
                self.cumulative_labels[component].setText(f"{component}: {self.cumulative_counts[component]}")
            else:
                # 动态添加新组件累计计数
                label = QLabel(f"{component}: {self.cumulative_counts[component]}")
                label.setAlignment(Qt.AlignCenter)
                label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: white;
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #e74c3c, stop:1 #c0392b);
                        border: 2px solid #a93226;
                        border-radius: 8px;
                        padding: 8px;
                        margin: 2px;
                        min-width: 100px;
                    }
                """)
                self.cumulative_layout.addWidget(label)
                self.cumulative_labels[component] = label

    def check_server_status(self):
        if self.video_thread and self.video_thread.isRunning():
            return

        try:
            response = requests.get(f"{self.api_url}/status", timeout=1)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'running':
                    # 自动重新连接
                    if not self.video_thread:
                        self.video_thread = VideoThread(self.rtmp_url, self.api_url)
                        self.video_thread.change_pixmap.connect(self.set_image)
                        self.video_thread.update_counts.connect(self.update_count_labels)
                        self.video_thread.start()
                        self.start_btn.setEnabled(False)
                        self.stop_btn.setEnabled(True)
                        self.status_bar.showMessage("🔄 已重新连接视频流")
                        self.video_label.setText("")
                else:
                    # 服务器空闲状态
                    if not self.start_btn.isEnabled():
                        self.start_btn.setEnabled(True)
                        self.stop_btn.setEnabled(False)
                        self.status_bar.showMessage("🟡 服务器连接正常，等待开始检测...")
            else:
                # 服务器响应异常
                if self.status_bar.currentMessage() != "❌ 服务器连接异常":
                    self.status_bar.showMessage("❌ 服务器连接异常")
        except:
            # 连接失败
            if self.status_bar.currentMessage() != "🔴 无法连接到服务器":
                self.status_bar.showMessage("🔴 无法连接到服务器")

    def closeEvent(self, event):
        if self.video_thread is not None:
            self.video_thread.stop()
        super().closeEvent(event)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())

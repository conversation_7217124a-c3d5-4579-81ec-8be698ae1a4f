@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 🚀 FFmpeg 自动安装脚本 for Windows 10
echo ================================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo ❌ 需要管理员权限来安装FFmpeg
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限确认
echo.

REM 创建FFmpeg目录
set FFMPEG_DIR=C:\ffmpeg
if not exist "%FFMPEG_DIR%" (
    echo 📁 创建FFmpeg目录: %FFMPEG_DIR%
    mkdir "%FFMPEG_DIR%"
)

echo 📥 正在下载FFmpeg...
echo 请手动完成以下步骤：
echo.
echo 1. 打开浏览器访问: https://www.gyan.dev/ffmpeg/builds/
echo 2. 下载 "release builds" 中的 "ffmpeg-release-essentials.zip"
echo 3. 解压到 C:\ffmpeg\ 目录
echo 4. 确保 C:\ffmpeg\bin\ffmpeg.exe 文件存在
echo.
echo 按任意键继续配置PATH环境变量...
pause >nul

REM 检查FFmpeg是否存在
if not exist "%FFMPEG_DIR%\bin\ffmpeg.exe" (
    echo ❌ 未找到FFmpeg可执行文件
    echo 请确保已将FFmpeg解压到 %FFMPEG_DIR% 目录
    pause
    exit /b 1
)

echo ✅ FFmpeg文件确认存在
echo.

REM 添加到PATH环境变量
echo 🔧 配置PATH环境变量...
setx PATH "%PATH%;%FFMPEG_DIR%\bin" /M
if errorlevel 1 (
    echo ❌ 配置PATH失败
    echo 请手动添加 %FFMPEG_DIR%\bin 到系统PATH
    pause
    exit /b 1
)

echo ✅ PATH环境变量配置完成
echo.

echo 🎉 FFmpeg安装完成！
echo.
echo 📝 安装信息：
echo    安装路径: %FFMPEG_DIR%
echo    可执行文件: %FFMPEG_DIR%\bin\ffmpeg.exe
echo    PATH已更新: %FFMPEG_DIR%\bin
echo.
echo ⚠️  重要提示：
echo    1. 请重启命令提示符或PowerShell
echo    2. 重启后运行 'ffmpeg -version' 验证安装
echo    3. 然后可以使用完整版启动脚本
echo.
pause

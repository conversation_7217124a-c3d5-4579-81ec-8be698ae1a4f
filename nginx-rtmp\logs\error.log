2025/04/12 15:45:33 [notice] 3184#5556: using the "select" event method
2025/04/12 15:45:33 [notice] 3184#5556: nginx/1.14.1
2025/04/12 15:45:33 [info] 3184#5556: OS: 260200 build:9200, "", suite:300, type:1
2025/04/12 15:45:33 [notice] 3184#5556: start worker processes
2025/04/12 15:45:33 [notice] 3184#5556: start worker process 6880
2025/04/12 15:45:33 [notice] 6880#21308: nginx/1.14.1
2025/04/12 15:45:33 [info] 6880#21308: OS: 260200 build:9200, "", suite:300, type:1
2025/04/12 15:45:33 [notice] 6880#21308: create thread 20052
2025/04/12 15:45:33 [notice] 6880#21308: create thread 3892
2025/04/12 15:45:33 [notice] 6880#21308: create thread 15700
2025/04/12 15:47:53 [notice] 2292#5172: using the "select" event method
2025/04/12 15:47:53 [notice] 2292#5172: nginx/1.14.1
2025/04/12 15:47:53 [info] 2292#5172: OS: 260200 build:9200, "", suite:300, type:1
2025/04/12 15:47:53 [notice] 2292#5172: start worker processes
2025/04/12 15:47:53 [notice] 2292#5172: start worker process 18148
2025/04/12 15:47:54 [notice] 18148#29300: nginx/1.14.1
2025/04/12 15:47:54 [info] 18148#29300: OS: 260200 build:9200, "", suite:300, type:1
2025/04/12 15:47:54 [notice] 18148#29300: create thread 22084
2025/04/12 15:47:54 [notice] 18148#29300: create thread 22612
2025/04/12 15:47:54 [notice] 18148#29300: create thread 21872
2025/04/12 15:50:32 [info] 18148#22084: *1 client connected '127.0.0.1'
2025/04/12 15:50:32 [info] 18148#22084: *1 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://127.0.0.1:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:50:32 [info] 18148#22084: *1 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:50:32 [info] 18148#22084: *1 play: name='test' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:50:38 [info] 18148#22084: *1 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:50:38 [info] 18148#22084: *1 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:50:38 [info] 18148#22084: *1 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:53:41 [info] 18148#22084: *2 client connected '127.0.0.1'
2025/04/12 15:53:41 [info] 18148#22084: *2 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://127.0.0.1:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:53:41 [info] 18148#22084: *2 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:53:41 [info] 18148#22084: *2 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:55:21 [info] 18148#22084: *3 client connected '127.0.0.1'
2025/04/12 15:55:21 [info] 18148#22084: *3 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://127.0.0.1:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:55:21 [info] 18148#22084: *3 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:55:21 [info] 18148#22084: *3 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:55:39 [info] 18148#22084: *3 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:55:39 [info] 18148#22084: *3 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:55:39 [info] 18148#22084: *3 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 15:59:23 [info] 18148#22084: *4 client connected '**********'
2025/04/12 15:59:23 [info] 18148#22084: *4 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://***********:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: **********, server: 0.0.0.0:1935
2025/04/12 15:59:23 [info] 18148#22084: *4 createStream, client: **********, server: 0.0.0.0:1935
2025/04/12 15:59:23 [info] 18148#22084: *4 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: **********, server: 0.0.0.0:1935
2025/04/12 16:00:40 [info] 18148#22084: *4 disconnect, client: **********, server: 0.0.0.0:1935
2025/04/12 16:00:40 [info] 18148#22084: *4 deleteStream, client: **********, server: 0.0.0.0:1935
2025/04/12 16:01:00 [info] 18148#22084: *2 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 16:01:00 [info] 18148#22084: *2 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 16:01:00 [info] 18148#22084: *2 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 16:01:00 [info] 18148#22084: *2 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:33:21 [info] 18148#22084: *5 client connected '127.0.0.1'
2025/04/12 19:33:21 [info] 18148#22084: *5 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://127.0.0.1:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:33:21 [info] 18148#22084: *5 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:33:21 [info] 18148#22084: *5 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:35:54 [info] 18148#22084: *6 client connected '127.0.0.1'
2025/04/12 19:35:54 [info] 18148#22084: *6 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:35:54 [info] 18148#22084: *6 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:35:54 [info] 18148#22084: *6 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:36:04 [info] 18148#22084: *6 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:36:04 [info] 18148#22084: *6 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:36:04 [info] 18148#22084: *6 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:36:24 [info] 18148#22084: *7 client connected '127.0.0.1'
2025/04/12 19:36:24 [info] 18148#22084: *7 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:36:24 [info] 18148#22084: *7 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:36:24 [info] 18148#22084: *7 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:36:49 [info] 18148#22084: *7 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:36:49 [info] 18148#22084: *7 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:36:49 [info] 18148#22084: *7 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:37:09 [info] 18148#22084: *8 client connected '**********'
2025/04/12 19:37:09 [info] 18148#22084: *8 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://***********:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: **********, server: 0.0.0.0:1935
2025/04/12 19:37:09 [info] 18148#22084: *8 createStream, client: **********, server: 0.0.0.0:1935
2025/04/12 19:37:09 [info] 18148#22084: *8 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: **********, server: 0.0.0.0:1935
2025/04/12 19:37:48 [info] 18148#22084: *8 disconnect, client: **********, server: 0.0.0.0:1935
2025/04/12 19:37:48 [info] 18148#22084: *8 deleteStream, client: **********, server: 0.0.0.0:1935
2025/04/12 19:38:08 [info] 18148#22084: *5 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:38:08 [info] 18148#22084: *5 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:38:08 [info] 18148#22084: *5 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:38:58 [info] 18148#22084: *9 client connected '127.0.0.1'
2025/04/12 19:38:58 [info] 18148#22084: *9 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://127.0.0.1:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:38:58 [info] 18148#22084: *9 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:38:58 [info] 18148#22084: *9 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:50:01 [info] 18148#22084: *10 client connected '127.0.0.1'
2025/04/12 19:50:01 [info] 18148#22084: *10 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:50:01 [info] 18148#22084: *10 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:50:01 [info] 18148#22084: *10 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:50:26 [info] 18148#22084: *10 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:50:26 [info] 18148#22084: *10 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:50:26 [info] 18148#22084: *10 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:53:17 [info] 18148#22084: *9 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:53:17 [info] 18148#22084: *9 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:53:17 [info] 18148#22084: *9 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:53:17 [info] 18148#22084: *9 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:54:17 [info] 18148#22084: *11 client connected '127.0.0.1'
2025/04/12 19:54:17 [info] 18148#22084: *11 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://127.0.0.1:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:54:17 [info] 18148#22084: *11 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:54:17 [info] 18148#22084: *11 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:55:02 [info] 18148#22084: *12 client connected '127.0.0.1'
2025/04/12 19:55:02 [info] 18148#22084: *12 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:55:02 [info] 18148#22084: *12 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:55:02 [info] 18148#22084: *12 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:55:03 [info] 18148#22084: *12 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:55:03 [info] 18148#22084: *12 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:55:44 [info] 18148#22084: *13 client connected '127.0.0.1'
2025/04/12 19:55:44 [info] 18148#22084: *13 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:55:44 [info] 18148#22084: *13 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:55:44 [info] 18148#22084: *13 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:55:45 [info] 18148#22084: *13 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:55:45 [info] 18148#22084: *13 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:56:39 [info] 18148#22084: *14 client connected '127.0.0.1'
2025/04/12 19:56:39 [info] 18148#22084: *14 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://127.0.0.1:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:56:39 [info] 18148#22084: *14 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:56:39 [info] 18148#22084: *14 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:56:41 [info] 18148#22084: *14 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:56:41 [info] 18148#22084: *14 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:58:52 [info] 18148#22084: *11 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:58:52 [info] 18148#22084: *11 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:58:52 [info] 18148#22084: *11 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 19:58:52 [info] 18148#22084: *11 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:06:22 [info] 18148#22084: *15 client connected '127.0.0.1'
2025/04/12 20:06:22 [info] 18148#22084: *15 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://127.0.0.1:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:06:22 [info] 18148#22084: *15 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:06:22 [info] 18148#22084: *15 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:06:22 [info] 18148#22084: *15 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:06:22 [info] 18148#22084: *15 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:06:22 [info] 18148#22084: *15 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:06:22 [info] 18148#22084: *15 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:30:47 [info] 18148#22084: *16 client connected '127.0.0.1'
2025/04/12 20:30:47 [info] 18148#22084: *16 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://127.0.0.1:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:30:47 [info] 18148#22084: *16 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:30:47 [info] 18148#22084: *16 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:30:48 [info] 18148#22084: *16 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:30:48 [info] 18148#22084: *16 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:30:48 [info] 18148#22084: *16 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/12 20:30:48 [info] 18148#22084: *16 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:24:25 [info] 18148#22084: *17 client connected '127.0.0.1'
2025/04/13 12:24:25 [info] 18148#22084: *17 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:24:25 [info] 18148#22084: *17 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:24:25 [info] 18148#22084: *17 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:25:16 [info] 18148#22084: *18 client connected '**********'
2025/04/13 12:25:16 [info] 18148#22084: *18 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://***********:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: **********, server: 0.0.0.0:1935
2025/04/13 12:25:16 [info] 18148#22084: *18 createStream, client: **********, server: 0.0.0.0:1935
2025/04/13 12:25:26 [info] 18148#22084: *18 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: **********, server: 0.0.0.0:1935
2025/04/13 12:31:20 [info] 18148#22084: *17 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:31:20 [info] 18148#22084: *17 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:31:20 [info] 18148#22084: *17 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:32:55 [info] 18148#22084: *18 disconnect, client: **********, server: 0.0.0.0:1935
2025/04/13 12:32:55 [info] 18148#22084: *18 deleteStream, client: **********, server: 0.0.0.0:1935
2025/04/13 12:33:57 [info] 18148#22084: *19 client connected '**********'
2025/04/13 12:33:57 [info] 18148#22084: *19 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://***********:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: **********, server: 0.0.0.0:1935
2025/04/13 12:33:57 [info] 18148#22084: *19 createStream, client: **********, server: 0.0.0.0:1935
2025/04/13 12:33:57 [info] 18148#22084: *19 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: **********, server: 0.0.0.0:1935
2025/04/13 12:35:40 [info] 18148#22084: *19 disconnect, client: **********, server: 0.0.0.0:1935
2025/04/13 12:35:40 [info] 18148#22084: *19 deleteStream, client: **********, server: 0.0.0.0:1935
2025/04/13 12:35:43 [info] 18148#22084: *20 client connected '**********'
2025/04/13 12:35:43 [info] 18148#22084: *20 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://***********:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: **********, server: 0.0.0.0:1935
2025/04/13 12:35:43 [info] 18148#22084: *20 createStream, client: **********, server: 0.0.0.0:1935
2025/04/13 12:35:43 [info] 18148#22084: *20 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: **********, server: 0.0.0.0:1935
2025/04/13 12:37:00 [info] 18148#22084: *20 disconnect, client: **********, server: 0.0.0.0:1935
2025/04/13 12:37:00 [info] 18148#22084: *20 deleteStream, client: **********, server: 0.0.0.0:1935
2025/04/13 12:37:21 [info] 18148#22084: *21 client connected '127.0.0.1'
2025/04/13 12:37:21 [info] 18148#22084: *21 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:37:21 [info] 18148#22084: *21 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:37:21 [info] 18148#22084: *21 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:37:21 [info] 18148#22084: *22 client connected '**********'
2025/04/13 12:37:21 [info] 18148#22084: *22 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://***********:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: **********, server: 0.0.0.0:1935
2025/04/13 12:37:21 [info] 18148#22084: *22 createStream, client: **********, server: 0.0.0.0:1935
2025/04/13 12:37:22 [info] 18148#22084: *22 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: **********, server: 0.0.0.0:1935
2025/04/13 12:41:15 [info] 18148#22084: *22 disconnect, client: **********, server: 0.0.0.0:1935
2025/04/13 12:41:15 [info] 18148#22084: *22 deleteStream, client: **********, server: 0.0.0.0:1935
2025/04/13 12:41:35 [info] 18148#22084: *21 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:41:35 [info] 18148#22084: *21 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/13 12:41:35 [info] 18148#22084: *21 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 10:25:18 [notice] 15172#14596: using the "select" event method
2025/04/18 10:25:18 [notice] 15172#14596: nginx/1.14.1
2025/04/18 10:25:18 [info] 15172#14596: OS: 260200 build:9200, "", suite:300, type:1
2025/04/18 10:25:18 [notice] 15172#14596: start worker processes
2025/04/18 10:25:18 [notice] 15172#14596: start worker process 1016
2025/04/18 10:25:18 [notice] 1016#13440: nginx/1.14.1
2025/04/18 10:25:18 [info] 1016#13440: OS: 260200 build:9200, "", suite:300, type:1
2025/04/18 10:25:18 [notice] 1016#13440: create thread 13640
2025/04/18 10:25:18 [notice] 1016#13440: create thread 344
2025/04/18 10:25:18 [notice] 1016#13440: create thread 20520
2025/04/18 10:32:45 [info] 1016#13640: *1 client connected '127.0.0.1'
2025/04/18 10:32:45 [info] 1016#13640: *1 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 10:32:45 [info] 1016#13640: *1 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 10:32:45 [info] 1016#13640: *1 play: name='stream' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 10:33:14 [info] 1016#13640: *1 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 10:33:14 [info] 1016#13640: *1 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 10:38:40 [notice] 12924#13080: using the "select" event method
2025/04/18 10:38:40 [notice] 12924#13080: nginx/1.14.1
2025/04/18 10:38:40 [info] 12924#13080: OS: 260200 build:9200, "", suite:300, type:1
2025/04/18 10:38:40 [notice] 12924#13080: start worker processes
2025/04/18 10:38:40 [notice] 12924#13080: start worker process 13144
2025/04/18 10:38:40 [notice] 13144#3768: nginx/1.14.1
2025/04/18 10:38:40 [info] 13144#3768: OS: 260200 build:9200, "", suite:300, type:1
2025/04/18 10:38:40 [notice] 13144#3768: create thread 13448
2025/04/18 10:38:40 [notice] 13144#3768: create thread 12560
2025/04/18 10:38:40 [notice] 13144#3768: create thread 1620
2025/04/18 11:06:26 [info] 13144#13448: *1 client connected '127.0.0.1'
2025/04/18 11:06:26 [info] 13144#13448: *1 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:06:26 [info] 13144#13448: *1 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:06:26 [info] 13144#13448: *1 play: name='stream' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:06:41 [info] 13144#13448: *2 client connected '127.0.0.1'
2025/04/18 11:06:41 [info] 13144#13448: *2 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:06:41 [info] 13144#13448: *2 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:06:41 [info] 13144#13448: *2 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:06:55 [info] 13144#13448: *1 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:06:55 [info] 13144#13448: *1 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:11:14 [info] 13144#13448: *3 client connected '***********'
2025/04/18 11:11:14 [info] 13144#13448: *3 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://***********:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: ***********, server: 0.0.0.0:1935
2025/04/18 11:11:23 [info] 13144#13448: *3 createStream, client: ***********, server: 0.0.0.0:1935
2025/04/18 11:11:23 [info] 13144#13448: *3 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: ***********, server: 0.0.0.0:1935
2025/04/18 11:11:38 [info] 13144#13448: *3 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: ***********, server: 0.0.0.0:1935
2025/04/18 11:11:38 [info] 13144#13448: *3 disconnect, client: ***********, server: 0.0.0.0:1935
2025/04/18 11:11:38 [info] 13144#13448: *3 deleteStream, client: ***********, server: 0.0.0.0:1935
2025/04/18 11:11:41 [info] 13144#13448: *4 client connected '***********'
2025/04/18 11:11:41 [info] 13144#13448: *4 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://***********:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: ***********, server: 0.0.0.0:1935
2025/04/18 11:11:41 [info] 13144#13448: *4 createStream, client: ***********, server: 0.0.0.0:1935
2025/04/18 11:11:41 [info] 13144#13448: *4 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: ***********, server: 0.0.0.0:1935
2025/04/18 11:13:23 [info] 13144#13448: *4 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: ***********, server: 0.0.0.0:1935
2025/04/18 11:13:23 [info] 13144#13448: *4 disconnect, client: ***********, server: 0.0.0.0:1935
2025/04/18 11:13:23 [info] 13144#13448: *4 deleteStream, client: ***********, server: 0.0.0.0:1935
2025/04/18 11:13:38 [info] 13144#13448: *2 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:13:38 [info] 13144#13448: *2 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:13:38 [info] 13144#13448: *2 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:13:38 [info] 13144#13448: *2 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:16:20 [info] 13144#13448: *5 client connected '127.0.0.1'
2025/04/18 11:16:20 [info] 13144#13448: *5 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:16:20 [info] 13144#13448: *5 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:16:20 [info] 13144#13448: *5 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:16:28 [info] 13144#13448: *6 client connected '127.0.0.1'
2025/04/18 11:16:28 [info] 13144#13448: *6 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:16:28 [info] 13144#13448: *6 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:16:28 [info] 13144#13448: *6 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:18:15 [info] 13144#13448: *6 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:18:15 [info] 13144#13448: *6 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:18:15 [info] 13144#13448: *6 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:18:15 [info] 13144#13448: *6 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:18:23 [info] 13144#13448: *5 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:18:23 [info] 13144#13448: *5 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:18:23 [info] 13144#13448: *5 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:19:33 [info] 13144#13448: *7 client connected '127.0.0.1'
2025/04/18 11:19:33 [info] 13144#13448: *7 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:19:33 [info] 13144#13448: *7 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:19:33 [info] 13144#13448: *7 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:19:42 [info] 13144#13448: *8 client connected '127.0.0.1'
2025/04/18 11:19:42 [info] 13144#13448: *8 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:19:42 [info] 13144#13448: *8 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:19:42 [info] 13144#13448: *8 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:20:48 [info] 13144#13448: *7 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:20:48 [info] 13144#13448: *7 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:20:48 [info] 13144#13448: *7 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:20:48 [info] 13144#13448: *7 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:06 [info] 13144#13448: *9 client connected '127.0.0.1'
2025/04/18 11:21:06 [info] 13144#13448: *9 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:06 [info] 13144#13448: *9 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:06 [info] 13144#13448: *9 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:43 [info] 13144#13448: *9 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:43 [info] 13144#13448: *9 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:43 [info] 13144#13448: *9 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:43 [info] 13144#13448: *9 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:47 [info] 13144#13448: *8 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:47 [info] 13144#13448: *8 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:47 [info] 13144#13448: *8 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:21:47 [info] 13144#13448: *8 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:25:48 [info] 13144#13448: *10 client connected '127.0.0.1'
2025/04/18 11:25:48 [info] 13144#13448: *10 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:25:48 [info] 13144#13448: *10 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:25:48 [info] 13144#13448: *10 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:25:56 [info] 13144#13448: *11 client connected '127.0.0.1'
2025/04/18 11:25:56 [info] 13144#13448: *11 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:25:56 [info] 13144#13448: *11 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:25:56 [info] 13144#13448: *11 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:26:24 [info] 13144#13448: *11 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:26:24 [info] 13144#13448: *11 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:26:24 [info] 13144#13448: *11 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:26:25 [info] 13144#13448: *10 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:26:25 [info] 13144#13448: *10 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:26:25 [info] 13144#13448: *10 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:26:25 [info] 13144#13448: *10 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:27:33 [info] 13144#13448: *12 client connected '127.0.0.1'
2025/04/18 11:27:33 [info] 13144#13448: *12 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:27:33 [info] 13144#13448: *12 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:27:33 [info] 13144#13448: *12 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:28:02 [info] 13144#13448: *12 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:28:02 [info] 13144#13448: *12 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:32:40 [info] 13144#13448: *13 client connected '127.0.0.1'
2025/04/18 11:32:40 [info] 13144#13448: *13 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:32:40 [info] 13144#13448: *13 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:32:40 [info] 13144#13448: *13 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:33:10 [info] 13144#13448: *13 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:33:10 [info] 13144#13448: *13 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:03 [info] 13144#13448: *14 client connected '127.0.0.1'
2025/04/18 11:34:03 [info] 13144#13448: *14 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:03 [info] 13144#13448: *14 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:03 [info] 13144#13448: *14 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:11 [info] 13144#13448: *15 client connected '127.0.0.1'
2025/04/18 11:34:11 [info] 13144#13448: *15 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:11 [info] 13144#13448: *15 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:11 [info] 13144#13448: *15 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:49 [info] 13144#13448: *14 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:49 [info] 13144#13448: *14 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:49 [info] 13144#13448: *14 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:58 [info] 13144#13448: *15 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:58 [info] 13144#13448: *15 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:58 [info] 13144#13448: *15 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:34:58 [info] 13144#13448: *15 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:36:52 [info] 13144#13448: *16 client connected '127.0.0.1'
2025/04/18 11:36:52 [info] 13144#13448: *16 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:36:52 [info] 13144#13448: *16 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:36:52 [info] 13144#13448: *16 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:37:04 [info] 13144#13448: *17 client connected '127.0.0.1'
2025/04/18 11:37:04 [info] 13144#13448: *17 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:37:04 [info] 13144#13448: *17 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:37:04 [info] 13144#13448: *17 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:37:26 [info] 13144#13448: *16 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:37:26 [info] 13144#13448: *16 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:37:26 [info] 13144#13448: *16 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:37:26 [info] 13144#13448: *16 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:37:31 [info] 13144#13448: *17 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:37:31 [info] 13144#13448: *17 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 11:37:31 [info] 13144#13448: *17 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:00:07 [info] 13144#13448: *18 client connected '127.0.0.1'
2025/04/18 14:00:07 [info] 13144#13448: *18 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:00:07 [info] 13144#13448: *18 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:00:07 [info] 13144#13448: *18 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:00:20 [info] 13144#13448: *19 client connected '127.0.0.1'
2025/04/18 14:00:20 [info] 13144#13448: *19 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:00:20 [info] 13144#13448: *19 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:00:20 [info] 13144#13448: *19 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:01:12 [info] 13144#13448: *19 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:01:12 [info] 13144#13448: *19 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:01:12 [info] 13144#13448: *19 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:01:42 [info] 13144#13448: *18 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:01:42 [info] 13144#13448: *18 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:01:42 [info] 13144#13448: *18 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:01:42 [info] 13144#13448: *18 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:03:48 [info] 13144#13448: *20 client connected '127.0.0.1'
2025/04/18 14:03:48 [info] 13144#13448: *20 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:03:48 [info] 13144#13448: *20 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:03:48 [info] 13144#13448: *20 play: name='stream1' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:04:18 [info] 13144#13448: *20 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:04:18 [info] 13144#13448: *20 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:22:31 [info] 13144#13448: *21 client connected '127.0.0.1'
2025/04/18 14:22:31 [info] 13144#13448: *21 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:22:31 [info] 13144#13448: *21 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:22:31 [info] 13144#13448: *21 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:24:00 [info] 13144#13448: *21 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:24:00 [info] 13144#13448: *21 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:24:00 [info] 13144#13448: *21 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:41:34 [info] 13144#13448: *22 client connected '127.0.0.1'
2025/04/18 14:41:34 [info] 13144#13448: *22 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:41:34 [info] 13144#13448: *22 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:41:34 [info] 13144#13448: *22 publish: name='stream1' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:41:55 [info] 13144#13448: *22 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:41:55 [info] 13144#13448: *22 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/04/18 14:41:55 [info] 13144#13448: *22 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:48:00 [notice] 16276#3696: using the "select" event method
2025/06/09 19:48:00 [notice] 16276#3696: nginx/1.14.1
2025/06/09 19:48:00 [info] 16276#3696: OS: 260200 build:9200, "", suite:100, type:1
2025/06/09 19:48:00 [notice] 16276#3696: start worker processes
2025/06/09 19:48:00 [notice] 16276#3696: start worker process 10488
2025/06/09 19:48:00 [notice] 10488#16148: nginx/1.14.1
2025/06/09 19:48:00 [info] 10488#16148: OS: 260200 build:9200, "", suite:100, type:1
2025/06/09 19:48:00 [notice] 10488#16148: create thread 9220
2025/06/09 19:48:00 [notice] 10488#16148: create thread 10736
2025/06/09 19:48:00 [notice] 10488#16148: create thread 13104
2025/06/09 19:48:12 [info] 10488#9220: *1 client connected '127.0.0.1'
2025/06/09 19:48:12 [info] 10488#9220: *1 connect: app='live' args='' flashver='LNX 9,0,124,2' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=4071 vcodecs=252 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:48:12 [info] 10488#9220: *1 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:48:12 [info] 10488#9220: *1 play: name='stream' args='' start=-2000 duration=0 reset=0 silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:48:16 [info] 10488#9220: *2 client connected '127.0.0.1'
2025/06/09 19:48:16 [info] 10488#9220: *2 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:48:16 [info] 10488#9220: *2 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:48:16 [info] 10488#9220: *2 publish: name='stream' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:55:48 [info] 10488#9220: *2 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host), client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:55:48 [info] 10488#9220: *2 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:55:48 [info] 10488#9220: *2 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:56:18 [info] 10488#9220: *1 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:56:18 [info] 10488#9220: *1 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:56:18 [info] 10488#9220: *1 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 19:56:18 [info] 10488#9220: *1 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 20:26:01 [notice] 10732#10572: using the "select" event method
2025/06/09 20:26:01 [notice] 10732#10572: nginx/1.14.1
2025/06/09 20:26:01 [info] 10732#10572: OS: 260200 build:9200, "", suite:100, type:1
2025/06/09 20:26:01 [notice] 10732#10572: start worker processes
2025/06/09 20:26:01 [notice] 10732#10572: start worker process 12656
2025/06/09 20:26:01 [notice] 12656#18852: nginx/1.14.1
2025/06/09 20:26:01 [info] 12656#18852: OS: 260200 build:9200, "", suite:100, type:1
2025/06/09 20:26:01 [notice] 12656#18852: create thread 18832
2025/06/09 20:26:01 [notice] 12656#18852: create thread 12172
2025/06/09 20:26:01 [notice] 12656#18852: create thread 5932
2025/06/09 20:28:37 [info] 10488#9220: *3 client connected '127.0.0.1'
2025/06/09 20:28:37 [info] 10488#9220: *3 connect: app='live' args='' flashver='FMLE/3.0 (compatible; Lavf61.7.' swf_url='' tc_url='rtmp://localhost:1935/live' page_url='' acodecs=0 vcodecs=0 object_encoding=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 20:28:37 [info] 10488#9220: *3 createStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 20:28:37 [info] 10488#9220: *3 publish: name='stream' args='' type=live silent=0, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 20:28:37 [info] 10488#9220: *3 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 20:28:37 [info] 10488#9220: *3 WSARecv() failed (10053: An established connection was aborted by the software in your host machine), client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 20:28:37 [info] 10488#9220: *3 disconnect, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 20:28:37 [info] 10488#9220: *3 deleteStream, client: 127.0.0.1, server: 0.0.0.0:1935
2025/06/09 20:31:51 [emerg] 17236#17720: invalid event type "epoll" in C:\Users\<USER>\Desktop\yolov8_count\server\nginx-rtmp\nginx.conf:7
2025/06/09 20:33:12 [emerg] 18012#17444: invalid event type "epoll" in C:\Users\<USER>\Desktop\yolov8_count\server\nginx-rtmp\nginx.conf:7
2025/06/09 20:50:57 [error] 6244#16548: *1 CreateFile() "C:\Users\<USER>\Desktop\yolov8_count\nginx-rtmp-win32-master\nginx-rtmp-win32-master/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /favicon.ico HTTP/1.1", host: "localhost:8080", referrer: "http://localhost:8080/stat"
2025/06/09 20:53:28 [error] 19028#10576: *1 CreateFile() "C:\Users\<USER>\Desktop\yolov8_count\nginx-rtmp-win32-master\nginx-rtmp-win32-master/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /favicon.ico HTTP/1.1", host: "localhost:8080", referrer: "http://localhost:8080/stat"
2025/06/09 20:53:52 [error] 19028#10576: *4 CreateFile() "C:\Users\<USER>\Desktop\yolov8_count\nginx-rtmp-win32-master\nginx-rtmp-win32-master/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /favicon.ico HTTP/1.1", host: "localhost:8080", referrer: "http://localhost:8080/stat"

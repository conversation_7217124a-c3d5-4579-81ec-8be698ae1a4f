#!/usr/bin/env python3
"""
YOLOv8 低延时检测系统 - 无FFmpeg启动脚本
适用于没有安装FFmpeg的环境
"""

import os
import sys
import time
import logging
import subprocess
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

def check_requirements():
    """检查基本要求"""
    logger.info("检查基本要求...")
    
    # 检查模型文件
    model_path = Path("server/models/best.pt")
    if not model_path.exists():
        logger.error("❌ 模型文件不存在")
        logger.info(f"请确保模型文件存在: {model_path}")
        return False
    
    logger.info("✅ 模型文件存在")
    logger.info("⚠️ 运行在无FFmpeg模式 - 视频流功能受限")
    return True

def start_flask_server():
    """启动Flask服务器"""
    logger.info("启动Flask API服务器...")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(Path.cwd())
        
        # 启动Flask服务器
        cmd = [sys.executable, "server/app.py"]
        process = subprocess.Popen(
            cmd,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待服务器启动
        time.sleep(3)
        
        if process.poll() is None:
            logger.info("✅ Flask服务器启动成功")
            logger.info("   API地址: http://localhost:5000")
            return process
        else:
            stdout, stderr = process.communicate()
            logger.error("❌ Flask服务器启动失败")
            if stderr:
                logger.error(f"错误信息: {stderr.decode()}")
            return None
            
    except Exception as e:
        logger.error(f"❌ 启动Flask服务器时出错: {e}")
        return None

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 YOLOv8 低延时检测系统 - 无FFmpeg模式")
    print("=" * 50)
    
    # 检查要求
    if not check_requirements():
        input("按回车键退出...")
        return
    
    # 启动Flask服务器
    flask_process = start_flask_server()
    if not flask_process:
        input("按回车键退出...")
        return
    
    logger.info("🎉 系统启动完成!")
    logger.info("📝 使用说明:")
    logger.info("   1. 系统运行在无FFmpeg模式")
    logger.info("   2. 可以使用API进行检测，但无视频流")
    logger.info("   3. 建议安装FFmpeg以获得完整功能")
    logger.info("   4. 按 Ctrl+C 停止系统")
    
    try:
        # 保持运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("🛑 正在停止系统...")
        
        # 停止Flask服务器
        if flask_process:
            flask_process.terminate()
            flask_process.wait()
        
        logger.info("✅ 系统已停止")

if __name__ == "__main__":
    main()

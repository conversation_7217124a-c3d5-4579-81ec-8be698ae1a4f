#!/usr/bin/env python3
"""
系统演示启动脚本
在没有FFmpeg的情况下演示系统功能
使用模拟数据和本地摄像头
"""

import os
import sys
import time
import threading
import subprocess
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 70)
    print("🚀 YOLOv8 电子元器件计数系统 - 演示模式")
    print("=" * 70)
    print("📝 演示模式说明:")
    print("   - 使用本地摄像头进行检测")
    print("   - 不依赖RTMP流")
    print("   - 展示完整的检测和计数功能")
    print("   - 支持累计计数和界面增强")
    print("=" * 70)

def check_camera():
    """检查摄像头可用性"""
    print("📷 检查摄像头...")
    
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                print("✅ 摄像头可用")
                return True
            else:
                print("⚠️ 摄像头无法读取图像")
                return False
        else:
            print("❌ 无法打开摄像头")
            return False
            
    except Exception as e:
        print(f"❌ 摄像头检查失败: {e}")
        return False

def start_demo_server():
    """启动演示服务器"""
    print("🚀 启动演示服务器...")
    
    try:
        # 使用无FFmpeg模式启动服务器
        process = subprocess.Popen(
            [sys.executable, "simple_start_no_ffmpeg.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待服务器启动
        time.sleep(5)
        
        if process.poll() is None:
            print("✅ 演示服务器启动成功")
            return process
        else:
            stdout, stderr = process.communicate()
            print("❌ 演示服务器启动失败")
            if stderr:
                print(f"错误: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ 启动演示服务器失败: {e}")
        return None

def start_demo_client():
    """启动演示客户端"""
    print("🖥️ 启动演示客户端...")
    
    try:
        process = subprocess.Popen(
            [sys.executable, "client/main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ 演示客户端启动成功")
            print("💡 请查看新打开的GUI窗口")
            return process
        else:
            stdout, stderr = process.communicate()
            print("❌ 演示客户端启动失败")
            if stderr:
                print(f"错误: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ 启动演示客户端失败: {e}")
        return None

def show_demo_instructions():
    """显示演示说明"""
    print("\n" + "=" * 70)
    print("🎮 演示操作指南")
    print("=" * 70)
    print("1. 📷 在客户端GUI中选择摄像头")
    print("2. ⚙️ 调整分辨率和帧率设置")
    print("3. 🚀 点击'开始检测'按钮")
    print("4. 📊 观察实时检测结果和计数")
    print("5. 📈 查看累计统计功能")
    print("6. 🔄 使用'重置累计'按钮清零计数")
    print("7. ⏹️ 点击'停止检测'结束")
    print()
    print("💡 提示:")
    print("   - 将电子元器件放在摄像头前进行检测")
    print("   - 系统会自动识别电阻、电容、LED等元件")
    print("   - 累计计数会记录检测到的最大数量")
    print("=" * 70)

def main():
    """主函数"""
    print_banner()
    
    # 切换到项目目录
    os.chdir(Path(__file__).parent)
    
    # 检查基本要求
    print("🔍 检查系统要求...")
    
    # 检查模型文件
    model_path = Path("server/models/best.pt")
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        print("请确保YOLOv8模型文件存在")
        input("按回车键退出...")
        return
    
    print("✅ 模型文件存在")
    
    # 检查摄像头
    if not check_camera():
        print("⚠️ 摄像头不可用，但系统仍可启动")
    
    # 启动演示服务器
    server_process = start_demo_server()
    if not server_process:
        print("❌ 无法启动服务器")
        input("按回车键退出...")
        return
    
    # 启动演示客户端
    client_process = start_demo_client()
    if not client_process:
        print("❌ 无法启动客户端")
        server_process.terminate()
        input("按回车键退出...")
        return
    
    # 显示操作指南
    show_demo_instructions()
    
    try:
        print("\n🎉 演示系统已启动！")
        print("按 Ctrl+C 停止演示系统")
        
        # 保持运行
        while True:
            time.sleep(1)
            
            # 检查进程状态
            if server_process.poll() is not None:
                print("⚠️ 服务器进程已停止")
                break
                
    except KeyboardInterrupt:
        print("\n🛑 正在停止演示系统...")
        
        # 停止进程
        if client_process and client_process.poll() is None:
            client_process.terminate()
            print("✅ 客户端已停止")
            
        if server_process and server_process.poll() is None:
            server_process.terminate()
            print("✅ 服务器已停止")
        
        print("🎉 演示系统已完全停止")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")

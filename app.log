2025-06-09 20:46:22,378 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-09 20:46:22,378 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:46:22,378 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-09 20:46:22,385 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 20:46:22,385 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 20:53:07,680 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-09 20:53:07,680 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:53:07,686 - INFO - Model: C:\Users\<USER>\Desktop\yolov8_count\server\models\best.pt
2025-06-09 20:53:07,690 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-09 20:53:07,690 - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 20:53:19,167 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET / HTTP/1.1" 200 -
2025-06-09 20:53:19,181 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/droid-sans.css HTTP/1.1" 200 -
2025-06-09 20:53:19,397 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui.css HTTP/1.1" 200 -
2025-06-09 20:53:19,477 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui-bundle.js HTTP/1.1" 200 -
2025-06-09 20:53:19,478 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/swagger-ui-standalone-preset.js HTTP/1.1" 200 -
2025-06-09 20:53:19,557 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swagger.json HTTP/1.1" 200 -
2025-06-09 20:53:19,888 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:19] "GET /swaggerui/favicon-32x32.png HTTP/1.1" 200 -
2025-06-09 20:53:55,852 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:55] "GET / HTTP/1.1" 200 -
2025-06-09 20:53:55,884 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:55] "[36mGET /swaggerui/droid-sans.css HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,108 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui.css HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,172 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui-bundle.js HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,173 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/swagger-ui-standalone-preset.js HTTP/1.1[0m" 304 -
2025-06-09 20:53:56,243 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "GET /swagger.json HTTP/1.1" 200 -
2025-06-09 20:53:56,570 - INFO - 127.0.0.1 - - [09/Jun/2025 20:53:56] "[36mGET /swaggerui/favicon-32x32.png HTTP/1.1[0m" 304 -
2025-06-09 20:55:01,668 - INFO - Received status request
2025-06-09 20:55:01,668 - INFO - Current status: stopped, counts: {}
2025-06-09 20:55:01,669 - INFO - 127.0.0.1 - - [09/Jun/2025 20:55:01] "GET /api/status HTTP/1.1" 200 -
2025-06-09 20:55:03,707 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '480x480', 'fps': 15}
2025-06-09 20:55:03,707 - INFO - Starting stream with params - camera: 0, resolution: 480x480, fps: 15
2025-06-09 20:55:03,708 - INFO - StreamThread-20250609205503 - Starting stream processing
2025-06-09 20:55:03,708 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-09 20:55:03,709 - INFO - 127.0.0.1 - - [09/Jun/2025 20:55:03] "POST /api/start HTTP/1.1" 200 -
2025-06-09 20:55:05,224 - INFO - StreamThread-20250609205503 - Camera initialized
2025-06-09 20:55:05,225 - INFO - StreamThread-20250609205503 - Streamer initialized
2025-06-09 20:55:12,239 - INFO - StreamThread-20250609205503 - Processed 100 frames, FPS: 14.26
2025-06-09 20:55:17,598 - INFO - StreamThread-20250609205503 - Processed 200 frames, FPS: 18.66
2025-06-09 20:55:22,878 - INFO - StreamThread-20250609205503 - Processed 300 frames, FPS: 18.94
2025-06-09 20:55:28,175 - INFO - StreamThread-20250609205503 - Processed 400 frames, FPS: 18.88
2025-06-09 20:55:33,455 - INFO - StreamThread-20250609205503 - Processed 500 frames, FPS: 18.94
2025-06-09 20:55:38,750 - INFO - StreamThread-20250609205503 - Processed 600 frames, FPS: 18.88
2025-06-09 20:55:44,047 - INFO - StreamThread-20250609205503 - Processed 700 frames, FPS: 18.88
2025-06-09 20:55:49,327 - INFO - StreamThread-20250609205503 - Processed 800 frames, FPS: 18.94
2025-06-11 20:56:37,762 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-11 20:56:37,762 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-11 20:56:37,762 - INFO - Model: E:\yolov8_count\server\models\best.pt
2025-06-11 20:56:37,774 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-11 20:56:37,775 - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 20:57:13,799 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-11 20:57:13,799 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-11 20:57:13,799 - INFO - Model: E:\yolov8_count\server\models\best.pt
2025-06-11 20:57:13,810 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-11 20:57:13,811 - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 22:19:38,215 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-11 22:19:38,215 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-11 22:19:38,216 - INFO - Model: E:\yolov8_count\server\models\best.pt
2025-06-11 22:19:38,216 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-11 22:19:38,216 - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 22:22:14,073 - INFO - Received status request
2025-06-11 22:22:14,073 - INFO - Current status: stopped, counts: {}
2025-06-11 22:22:14,073 - INFO - 127.0.0.1 - - [11/Jun/2025 22:22:14] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:22:16,059 - INFO - Received status request
2025-06-11 22:22:16,059 - INFO - Current status: stopped, counts: {}
2025-06-11 22:22:16,059 - INFO - 127.0.0.1 - - [11/Jun/2025 22:22:16] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:22:18,072 - INFO - Received status request
2025-06-11 22:22:18,072 - INFO - Current status: stopped, counts: {}
2025-06-11 22:22:18,072 - INFO - 127.0.0.1 - - [11/Jun/2025 22:22:18] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:22:19,086 - INFO - Received status request
2025-06-11 22:22:19,086 - INFO - Current status: stopped, counts: {}
2025-06-11 22:22:19,086 - INFO - 127.0.0.1 - - [11/Jun/2025 22:22:19] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:22:21,067 - INFO - Received status request
2025-06-11 22:22:21,067 - INFO - Current status: stopped, counts: {}
2025-06-11 22:22:21,067 - INFO - 127.0.0.1 - - [11/Jun/2025 22:22:21] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:22:23,095 - INFO - Received status request
2025-06-11 22:22:23,095 - INFO - Current status: stopped, counts: {}
2025-06-11 22:22:23,095 - INFO - 127.0.0.1 - - [11/Jun/2025 22:22:23] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:22:24,100 - INFO - Received status request
2025-06-11 22:22:24,100 - INFO - Current status: stopped, counts: {}
2025-06-11 22:22:24,100 - INFO - 127.0.0.1 - - [11/Jun/2025 22:22:24] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:22:26,104 - INFO - Received status request
2025-06-11 22:22:26,104 - INFO - Current status: stopped, counts: {}
2025-06-11 22:22:26,104 - INFO - 127.0.0.1 - - [11/Jun/2025 22:22:26] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:22:28,134 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x640', 'fps': 30}
2025-06-11 22:22:28,134 - INFO - Starting stream with params - camera: 0, resolution: 640x640, fps: 30
2025-06-11 22:22:28,134 - INFO - StreamThread-20250611222228 - Starting stream processing
2025-06-11 22:22:28,134 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-11 22:22:28,134 - INFO - 127.0.0.1 - - [11/Jun/2025 22:22:28] "POST /api/start HTTP/1.1" 200 -
2025-06-11 22:22:32,016 - INFO - StreamThread-20250611222228 - Camera initialized
2025-06-11 22:23:39,061 - INFO - Received stop request
2025-06-11 22:23:39,061 - INFO - Stopping stream...
2025-06-11 22:32:03,572 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-11 22:32:03,572 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-11 22:32:03,572 - INFO - Model: E:\yolov8_count\server\models\best.pt
2025-06-11 22:32:03,582 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-11 22:32:03,582 - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 22:32:48,619 - INFO - Received status request
2025-06-11 22:32:48,619 - INFO - Current status: stopped, counts: {}
2025-06-11 22:32:48,619 - INFO - 127.0.0.1 - - [11/Jun/2025 22:32:48] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:32:50,842 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x640', 'fps': 30}
2025-06-11 22:32:50,842 - INFO - Starting stream with params - camera: 0, resolution: 640x640, fps: 30
2025-06-11 22:32:50,842 - INFO - StreamThread-20250611223250 - Starting stream processing
2025-06-11 22:32:50,842 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-11 22:32:50,842 - INFO - 127.0.0.1 - - [11/Jun/2025 22:32:50] "POST /api/start HTTP/1.1" 200 -
2025-06-11 22:32:52,847 - INFO - Received status request
2025-06-11 22:32:52,847 - INFO - Current status: running, counts: {}
2025-06-11 22:32:52,847 - INFO - 127.0.0.1 - - [11/Jun/2025 22:32:52] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:32:54,707 - INFO - StreamThread-20250611223250 - Camera initialized

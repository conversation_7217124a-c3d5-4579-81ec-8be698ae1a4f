#!/usr/bin/env python3
"""
FFmpeg 安装验证脚本
检查FFmpeg是否正确安装并可用
"""

import subprocess
import sys
import os
from pathlib import Path

def check_ffmpeg():
    """检查FFmpeg安装状态"""
    print("=" * 50)
    print("🔍 FFmpeg 安装验证")
    print("=" * 50)
    
    # 检查命令行可用性
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        
        if result.returncode == 0:
            print("✅ FFmpeg 命令行可用")
            
            # 提取版本信息
            version_line = result.stdout.split('\n')[0]
            print(f"📋 版本信息: {version_line}")
            
            # 检查编码器支持
            encoders_result = subprocess.run(['ffmpeg', '-encoders'], 
                                           capture_output=True, 
                                           text=True, 
                                           timeout=10)
            
            if 'libx264' in encoders_result.stdout:
                print("✅ H.264编码器支持")
            else:
                print("⚠️  H.264编码器可能不可用")
                
            return True
            
        else:
            print("❌ FFmpeg命令执行失败")
            print(f"错误代码: {result.returncode}")
            return False
            
    except FileNotFoundError:
        print("❌ FFmpeg未找到")
        print("请确保FFmpeg已安装并添加到PATH")
        return False
        
    except subprocess.TimeoutExpired:
        print("❌ FFmpeg命令超时")
        return False
        
    except Exception as e:
        print(f"❌ 检查FFmpeg时出错: {e}")
        return False

def check_path():
    """检查PATH环境变量"""
    print("\n🔍 检查PATH环境变量...")
    
    path_dirs = os.environ.get('PATH', '').split(os.pathsep)
    ffmpeg_paths = [p for p in path_dirs if 'ffmpeg' in p.lower()]
    
    if ffmpeg_paths:
        print("✅ 在PATH中找到FFmpeg路径:")
        for path in ffmpeg_paths:
            print(f"   📁 {path}")
            
            # 检查可执行文件
            ffmpeg_exe = Path(path) / "ffmpeg.exe"
            if ffmpeg_exe.exists():
                print(f"   ✅ 找到可执行文件: {ffmpeg_exe}")
            else:
                print(f"   ❌ 未找到可执行文件: {ffmpeg_exe}")
    else:
        print("❌ PATH中未找到FFmpeg路径")

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        # 测试帮助命令
        result = subprocess.run(['ffmpeg', '-h'], 
                              capture_output=True, 
                              text=True, 
                              timeout=5)
        
        if result.returncode == 0:
            print("✅ 帮助命令正常")
        else:
            print("❌ 帮助命令失败")
            
        # 测试格式列表
        result = subprocess.run(['ffmpeg', '-formats'], 
                              capture_output=True, 
                              text=True, 
                              timeout=5)
        
        if result.returncode == 0 and 'mp4' in result.stdout:
            print("✅ 格式支持正常")
        else:
            print("❌ 格式支持异常")
            
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")

def main():
    """主函数"""
    print("FFmpeg 安装验证工具")
    print("=" * 30)
    
    # 检查FFmpeg
    ffmpeg_ok = check_ffmpeg()
    
    # 检查PATH
    check_path()
    
    # 测试功能
    if ffmpeg_ok:
        test_basic_functionality()
    
    print("\n" + "=" * 50)
    if ffmpeg_ok:
        print("🎉 FFmpeg 安装验证成功！")
        print("现在可以使用完整版启动脚本了")
    else:
        print("❌ FFmpeg 安装验证失败")
        print("请检查安装步骤或重新安装")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")

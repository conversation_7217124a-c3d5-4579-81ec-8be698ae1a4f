#!/usr/bin/env python3
"""
FFmpeg自动下载和安装脚本
适用于Windows 10
"""

import os
import sys
import zipfile
import requests
from pathlib import Path
import subprocess

def download_file(url, filename):
    """下载文件并显示进度"""
    print(f"📥 正在下载: {filename}")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r📊 下载进度: {percent:.1f}% ({downloaded}/{total_size} bytes)", end='')
        
        print(f"\n✅ 下载完成: {filename}")
        return True
        
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        return False

def extract_zip(zip_path, extract_to):
    """解压ZIP文件"""
    print(f"📦 正在解压: {zip_path}")
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        
        print(f"✅ 解压完成: {extract_to}")
        return True
        
    except Exception as e:
        print(f"❌ 解压失败: {e}")
        return False

def add_to_path(directory):
    """添加目录到系统PATH"""
    print(f"🔧 添加到PATH: {directory}")
    
    try:
        # 使用setx命令添加到系统PATH
        cmd = f'setx PATH "%PATH%;{directory}" /M'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PATH更新成功")
            return True
        else:
            print(f"❌ PATH更新失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ PATH更新出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 FFmpeg 自动下载和安装工具")
    print("=" * 60)
    
    # 检查管理员权限
    try:
        # 尝试写入系统目录来检查权限
        test_file = Path("C:\\Windows\\Temp\\ffmpeg_test.txt")
        test_file.write_text("test")
        test_file.unlink()
        print("✅ 管理员权限确认")
    except:
        print("❌ 需要管理员权限")
        print("请右键点击此脚本，选择'以管理员身份运行'")
        input("按回车键退出...")
        return
    
    # 设置路径
    ffmpeg_dir = Path("C:/ffmpeg")
    ffmpeg_bin = ffmpeg_dir / "bin"
    
    # 创建目录
    ffmpeg_dir.mkdir(exist_ok=True)
    print(f"📁 FFmpeg安装目录: {ffmpeg_dir}")
    
    # FFmpeg下载URL（使用稳定的镜像源）
    ffmpeg_url = "https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
    zip_filename = "ffmpeg-release-essentials.zip"
    
    print("\n📥 开始下载FFmpeg...")
    print("注意: 文件较大(约50MB)，请耐心等待")
    
    # 下载FFmpeg
    if not download_file(ffmpeg_url, zip_filename):
        print("❌ 下载失败，请检查网络连接")
        input("按回车键退出...")
        return
    
    # 解压文件
    if not extract_zip(zip_filename, str(ffmpeg_dir)):
        print("❌ 解压失败")
        input("按回车键退出...")
        return
    
    # 查找解压后的bin目录
    extracted_dirs = list(ffmpeg_dir.glob("ffmpeg-*"))
    if extracted_dirs:
        actual_bin = extracted_dirs[0] / "bin"
        if actual_bin.exists():
            # 移动文件到正确位置
            if not ffmpeg_bin.exists():
                ffmpeg_bin.mkdir(parents=True)
            
            for file in actual_bin.glob("*"):
                target = ffmpeg_bin / file.name
                if not target.exists():
                    file.rename(target)
            
            print(f"✅ FFmpeg文件已安装到: {ffmpeg_bin}")
        else:
            print("❌ 未找到bin目录")
            input("按回车键退出...")
            return
    else:
        print("❌ 未找到解压的FFmpeg目录")
        input("按回车键退出...")
        return
    
    # 验证安装
    ffmpeg_exe = ffmpeg_bin / "ffmpeg.exe"
    if ffmpeg_exe.exists():
        print(f"✅ FFmpeg可执行文件确认: {ffmpeg_exe}")
    else:
        print("❌ FFmpeg可执行文件未找到")
        input("按回车键退出...")
        return
    
    # 添加到PATH
    if add_to_path(str(ffmpeg_bin)):
        print("✅ PATH环境变量已更新")
    else:
        print("⚠️ PATH更新失败，请手动添加:")
        print(f"   {ffmpeg_bin}")
    
    # 清理下载文件
    try:
        os.remove(zip_filename)
        print("🗑️ 清理下载文件")
    except:
        pass
    
    print("\n" + "=" * 60)
    print("🎉 FFmpeg安装完成！")
    print("=" * 60)
    print()
    print("📝 安装信息:")
    print(f"   安装路径: {ffmpeg_dir}")
    print(f"   可执行文件: {ffmpeg_exe}")
    print(f"   PATH已添加: {ffmpeg_bin}")
    print()
    print("⚠️ 重要提示:")
    print("   1. 请重启命令提示符或PowerShell")
    print("   2. 运行 'ffmpeg -version' 验证安装")
    print("   3. 然后可以使用完整版启动脚本")
    print()
    print("🚀 下一步:")
    print("   运行 python setup_complete_system.py 进行系统测试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
